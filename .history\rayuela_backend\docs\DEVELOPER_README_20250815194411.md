# Rayuela API - Developer Documentation

Welcome to the Rayuela API developer documentation! This is your central hub for integrating Rayuela's recommendation engine into your applications.

## 🚀 Quick Start

New to Rayuela? Start here:

1. **[General Quick Start Guide](QUICKSTART.md)** - Complete setup from registration to first recommendations
2. **[Language-Specific Quickstarts](quickstart/)** - Ready-to-use examples in your preferred language:
   - [PHP](quickstart/php/README.md) - Complete PHP integration guide with examples
   - [Python](quickstart/python/) - Python SDK and examples
   - [Node.js](quickstart/nodejs/) - JavaScript/TypeScript integration

## 📚 API Reference

### Interactive Documentation
- **[Swagger UI](/api/docs)** - Interactive API explorer with "Try it out" functionality
- **[OpenAPI JSON](/api/openapi.json)** - Machine-readable API specification
- **[ReDoc](/api/redoc)** - Clean, alternative documentation interface

### Core API Guides
- **[Recommendations API](api/recommendations.md)** - Complete guide to recommendation endpoints
- **[Data Ingestion Guide](guides/data_ingestion_guide.md)** - How to load products, users, and interactions
- **[Pipeline API](api/pipeline.md)** - Model training and management

## 🔧 Integration Guides

### Essential Concepts
- **[API Key vs JWT Authentication](API_KEY_JWT_DIFFERENCE.md)** - When to use each authentication method
- **[Interaction Model](INTERACTION_MODEL.md)** - Understanding user-product interactions
- **[API Key Security](API_KEY_SECURITY.md)** - Best practices for secure API key usage

### Advanced Features
- **[Cold Start Preferences](cold_start_preferences_api.md)** - Handling new users without interaction history
- **[Product Features Enhancement](PRODUCT_FEATURES_ENHANCEMENT.md)** - Advanced product attribute handling

## 📖 Comprehensive Guides

### Data Management
- **[Data Ingestion Guide](guides/data_ingestion_guide.md)** - Batch and real-time data loading
- **[Soft Deletes](SOFT_DELETES.md)** - Managing deleted products and users
- **[High Volume Tables](HIGH_VOLUME_TABLES.md)** - Optimizing for large datasets

### Architecture & Performance
- **[Architecture Overview](ARCHITECTURE.md)** - System design and components
- **[Centralized Cache Strategy](CENTRALIZED_CACHE_STRATEGY.md)** - Performance optimization
- **[Database Indices](DATABASE_INDICES.md)** - Query optimization

### Security
- **[Security Configuration](SECURITY_CONFIG.md)** - Security best practices
- **[IP Protection Guide](IP_PROTECTION_GUIDE.md)** - Protecting your intellectual property
- **[Secrets Management](SECRETS_MANAGEMENT.md)** - Secure credential handling

## 🛠️ Development & Testing

### Testing
- **[Critical Testing Strategy](CRITICAL_TESTING_STRATEGY.md)** - Testing recommendations and ML models
- **[Local Testing](local_testing.md)** - Setting up local development environment
- **[CI/CD Testing](ci_cd_testing.md)** - Automated testing in pipelines

### Deployment
- **[Server Configuration](server_configuration.md)** - Production deployment guide
- **[Testing Production Guide](TESTING_PRODUCTION_GUIDE.md)** - Production testing strategies

## 📊 API Conventions

### Pagination
All list endpoints support consistent pagination:
- **Query Parameters**: Use `skip` and `limit` for simple pagination
- **Response Format**: All paginated responses include `items`, `total`, `page`, and `size`

```bash
GET /api/v1/products?skip=20&limit=10
```

### Filtering
Rayuela supports two filtering approaches:

#### Simple Filters (Query Parameters)
For basic filtering, use query parameters:
```bash
GET /api/v1/recommendations/most-sold?category=electronics&timeframe=week
```

#### Complex Filters (Request Body)
For advanced filtering with logical operators, use POST endpoints with structured filters:
```json
{
  "filters": {
    "logic": "and",
    "filters": [
      {"field": "price", "op": "lt", "value": 50},
      {"field": "category", "op": "eq", "value": "electronics"}
    ]
  }
}
```

**When to use each:**
- **Query Parameters**: Simple filters, single conditions, GET endpoints
- **Request Body**: Complex filters, multiple conditions with AND/OR logic, nested filters

### Error Handling
All API errors follow a consistent format:
```json
{
  "error_code": "USER_NOT_FOUND",
  "message": "User with ID 123 not found",
  "details": {}
}
```

Common error codes:
- `USER_NOT_FOUND` - User doesn't exist
- `MODEL_NOT_TRAINED` - No trained model available
- `RATE_LIMIT_EXCEEDED` - API rate limit exceeded
- `INVALID_FILTER` - Malformed filter structure

## 🔗 External Resources

### Code Examples
- **[Examples Repository](https://github.com/rayuela-examples)** - Complete integration examples
- **[Frontend Examples](examples/)** - React/Next.js integration examples

### Support
- **[Error Codes Reference](ERROR_CODES.md)** - Complete error code documentation
- **[FAQ](https://docs.rayuela.ai/faq)** - Frequently asked questions
- **[Support](mailto:<EMAIL>)** - Technical support contact

## 🔄 Migration Guides

- **[CamelCase Migration Guide](CAMELCASE_MIGRATION_GUIDE.md)** - Updating to camelCase API responses
- **[Schema Cleanup Fixes](SCHEMA_CLEANUP_FIXES.md)** - Breaking changes and migrations

## 📋 Business & Legal

- **[Subscription Model](SUBSCRIPTION_MODEL_SIMPLIFICATION.md)** - Pricing and usage limits
- **[Legal Texts Structure](textos_legales_estructura.md)** - Terms of service and privacy policy

---

## Need Help?

1. **Check the [Interactive API Docs](/api/docs)** - Test endpoints directly
2. **Browse [Code Examples](examples/)** - See working implementations
3. **Read the [Guides](guides/)** - In-depth integration tutorials
4. **Contact [Support](mailto:<EMAIL>)** - Get personalized help

**Happy coding! 🚀**
