# Rayuela API Conventions

This document outlines the consistent patterns and conventions used across the Rayuela API to help developers understand when and how to use different features.

## Pagination

All list endpoints in Rayuela support consistent pagination using query parameters.

### Standard Pagination Parameters

- **`skip`** (integer, default: 0): Number of items to skip (offset)
- **`limit`** (integer, default: 10, max: 100): Maximum number of items to return

### Pagination Response Format

All paginated responses follow this structure:

```json
{
  "items": [...],           // Array of actual data items
  "total": 150,            // Total number of items available
  "page": 3,               // Current page number (calculated from skip/limit)
  "size": 10               // Number of items per page (same as limit)
}
```

### Examples

```bash
# Get first 10 products (default)
GET /api/v1/products

# Get products 21-30 (page 3 with 10 items per page)
GET /api/v1/products?skip=20&limit=10

# Get first 50 products
GET /api/v1/products?limit=50
```

## Filtering

Rayuela supports two distinct filtering approaches depending on the complexity of your needs.

### Simple Filters (Query Parameters)

Use query parameters for basic, single-condition filters. This approach is ideal for:
- Single field filtering
- Simple equality or basic comparisons
- GET endpoints
- Quick filtering without complex logic

#### Examples

```bash
# Filter products by category
GET /api/v1/products?category=electronics

# Filter recommendations by timeframe and category
GET /api/v1/recommendations/most-sold?timeframe=week&category=electronics

# Filter with pagination
GET /api/v1/products?category=electronics&skip=0&limit=20
```

#### Supported Simple Filter Parameters

Common query parameters across endpoints:
- `category` - Filter by product category
- `timeframe` - Time period for analytics ("day", "week", "month")
- `min_ratings` - Minimum number of ratings required
- `include_explanation` - Include recommendation explanations (boolean)

### Complex Filters (Request Body)

Use structured filters in the request body for advanced filtering scenarios:
- Multiple conditions with AND/OR logic
- Nested filter groups
- Complex field operations
- POST endpoints with `/query` suffix

#### Filter Structure

```json
{
  "filters": {
    "logic": "and|or",
    "filters": [
      {
        "field": "field_name",
        "op": "operator",
        "value": "filter_value"
      },
      // ... more filters or nested groups
    ]
  }
}
```

#### Supported Operators

- **`eq`** - Equals
- **`neq`** - Not equals
- **`lt`** - Less than
- **`lte`** - Less than or equal
- **`gt`** - Greater than
- **`gte`** - Greater than or equal
- **`in`** - Value in list
- **`not_in`** - Value not in list
- **`contains`** - String contains (case-insensitive)
- **`starts_with`** - String starts with
- **`ends_with`** - String ends with

#### Complex Filter Examples

**Simple AND condition:**
```json
{
  "filters": {
    "logic": "and",
    "filters": [
      {"field": "price", "op": "lt", "value": 100},
      {"field": "category", "op": "eq", "value": "electronics"}
    ]
  }
}
```

**Nested OR within AND:**
```json
{
  "filters": {
    "logic": "and",
    "filters": [
      {"field": "price", "op": "lt", "value": 100},
      {
        "logic": "or",
        "filters": [
          {"field": "brand", "op": "eq", "value": "Samsung"},
          {"field": "brand", "op": "eq", "value": "Apple"}
        ]
      }
    ]
  }
}
```

**Using IN operator:**
```json
{
  "filters": {
    "logic": "and",
    "filters": [
      {"field": "category", "op": "in", "value": ["electronics", "accessories"]},
      {"field": "price", "op": "gte", "value": 50}
    ]
  }
}
```

### When to Use Each Approach

| Use Query Parameters When | Use Request Body When |
|---------------------------|----------------------|
| Single field filtering | Multiple field filtering |
| Simple equality checks | Complex logical operations (AND/OR) |
| GET endpoints | POST endpoints |
| Quick, straightforward filters | Nested filter groups |
| URL-friendly filtering | Advanced filter operators |

## Context and Metadata

Many endpoints support additional context to improve recommendations and results.

### Recommendation Context

When making recommendation requests, you can provide context to improve relevance:

```json
{
  "context": {
    "page_type": "home|product_detail|category|cart|checkout",
    "device": "desktop|mobile|tablet",
    "source_item_id": 123,
    "recently_viewed_ids": [456, 789],
    "session_id": "session_abc123"
  }
}
```

### Common Context Fields

- **`page_type`** - Where the recommendation will be displayed
- **`device`** - User's device type for responsive recommendations
- **`source_item_id`** - Current product being viewed (for related products)
- **`recently_viewed_ids`** - Recently viewed products for diversity
- **`session_id`** - Session identifier for tracking

## Error Handling

All API errors follow a consistent format:

```json
{
  "error_code": "ERROR_CODE_CONSTANT",
  "message": "Human-readable error description",
  "details": {
    // Additional error context when available
  }
}
```

### Common Error Codes

- **`USER_NOT_FOUND`** - User doesn't exist in the system
- **`MODEL_NOT_TRAINED`** - No trained model available for recommendations
- **`RATE_LIMIT_EXCEEDED`** - API rate limit exceeded
- **`INVALID_FILTER`** - Malformed filter structure
- **`RESOURCE_NOT_FOUND`** - Requested resource doesn't exist
- **`VALIDATION_ERROR`** - Request validation failed

## Authentication

Rayuela supports two authentication methods:

### API Key Authentication
For programmatic access and production integrations:
```bash
curl -H "X-API-Key: sk_your_api_key_here" \
     https://api.rayuela.ai/api/v1/recommendations/...
```

### JWT Token Authentication
For dashboard and user management:
```bash
curl -H "Authorization: Bearer your_jwt_token_here" \
     https://api.rayuela.ai/api/v1/accounts/...
```

## Rate Limiting

Rate limits are enforced per API key and vary by subscription plan:
- **Free Plan**: 1,000 requests/hour
- **Pro Plan**: 10,000 requests/hour
- **Enterprise**: Custom limits

Rate limit headers are included in all responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

## Versioning

The API uses URL path versioning:
- Current version: `/api/v1/`
- All endpoints are prefixed with the version
- Breaking changes will result in a new version (e.g., `/api/v2/`)

## Content Types

- **Request Content-Type**: `application/json`
- **Response Content-Type**: `application/json`
- **Character Encoding**: UTF-8

## Best Practices

1. **Use pagination** for large datasets to improve performance
2. **Implement retry logic** with exponential backoff for transient errors
3. **Cache responses** when appropriate to reduce API calls
4. **Use complex filters** only when necessary; prefer simple query parameters
5. **Include context** in recommendation requests for better results
6. **Handle rate limits** gracefully with proper error handling
7. **Validate responses** and handle edge cases (empty results, etc.)

## Examples by Use Case

### E-commerce Product Listing
```bash
# Simple category filter with pagination
GET /api/v1/products?category=electronics&skip=0&limit=20
```

### Advanced Product Search
```bash
POST /api/v1/products/query
Content-Type: application/json

{
  "filters": {
    "logic": "and",
    "filters": [
      {"field": "price", "op": "gte", "value": 100},
      {"field": "price", "op": "lte", "value": 500},
      {"field": "category", "op": "in", "value": ["electronics", "accessories"]}
    ]
  },
  "skip": 0,
  "limit": 20
}
```

### Personalized Recommendations
```bash
POST /api/v1/recommendations/personalized/query
Content-Type: application/json

{
  "user_id": 123,
  "filters": {
    "logic": "and",
    "filters": [
      {"field": "price", "op": "lt", "value": 200},
      {"field": "category", "op": "eq", "value": "electronics"}
    ]
  },
  "context": {
    "page_type": "home",
    "device": "mobile"
  },
  "limit": 10
}
```

---

For more detailed examples and integration guides, see the [Developer Documentation Hub](DEVELOPER_README.md).
