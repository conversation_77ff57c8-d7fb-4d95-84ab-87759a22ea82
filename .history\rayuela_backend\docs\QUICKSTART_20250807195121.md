# Guía de Inicio Rápido - Sistema de Recomendación

Esta guía te ayudará a comenzar a utilizar el sistema de recomendación en minutos. Sigue estos pasos para registrarte, verificar tu email, obtener tu API Key, cargar datos y obtener tus primeras recomendaciones.

## 1. Registro (Signup)

1. Visita [https://app.rayuela.example.com/register](https://app.rayuela.example.com/register)
2. Completa el formulario de registro con tu información:
   - Nombre de la cuenta/empresa
   - Tu email
   - Contraseña segura
3. Haz clic en "Crear Cuenta"
4. Verás un mensaje de confirmación indicando que se ha enviado un email de verificación

## 2. Verificación de Email

1. Revisa tu bandeja de entrada para encontrar el email de verificación de Rayuela
2. Haz clic en el enlace de verificación incluido en el email
3. Serás redirigido a una página de confirmación indicando que tu email ha sido verificado

**Nota:** Si no recibes el email de verificación, puedes solicitar un reenvío desde la página de login haciendo clic en "Reenviar email de verificación".

## 3. Inicio de Sesión (Login)

1. Una vez verificado tu email, visita [https://app.rayuela.example.com/login](https://app.rayuela.example.com/login)
2. Ingresa tu email y contraseña
3. Haz clic en "Ingresar"

## 4. Obtención de API Key

Al iniciar sesión por primera vez, se te mostrará automáticamente un modal con tu API Key inicial:

1. Copia tu API Key (¡importante!)
2. Guárdala en un lugar seguro, ya que **solo se mostrará una vez**
3. Haz clic en "He guardado mi API Key" para continuar al dashboard

Para ver tu API Key parcial posteriormente:

1. Ve a la sección "API Keys" en el dashboard
2. Verás una versión parcial de tu API Key (primeros y últimos caracteres)
3. Si necesitas regenerar tu API Key, puedes hacerlo desde esta página, pero ten en cuenta que la API Key anterior dejará de funcionar inmediatamente

**Nota:** También puedes ver una versión parcial de tu API Key en la sección "Usage" o en el Dashboard principal.

### Obtención de API Key vía API (Alternativa)

```bash
# Registra una nueva cuenta para obtener tu API Key
curl -X POST https://api.rayuela.example.com/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TuContraseñaSegura123!"
  }'
```

Guarda la API Key de la respuesta:

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "account_id": 123,
  "is_admin": false,
  "api_key": "sk_aBcDeFgHiJkLmNoPqRsTuVwXyZ0123456789", // IMPORTANTE: Guarda esta API Key, solo se muestra una vez
  "message": "Esta es tu primera API Key. Guárdala en un lugar seguro, solo se mostrará una vez."
}
```

Si necesitas regenerar tu API Key:

```bash
curl -X POST https://api.rayuela.example.com/api/v1/api-keys/ \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "X-API-Key: tu_api_key_actual" \
  -H "Content-Type: application/json"
```

## 5. Primera Llamada a la API

Una vez que tengas tu API Key, puedes comenzar a utilizar la API. Aquí hay un ejemplo simple para verificar que tu API Key funciona correctamente:

```bash
curl -X GET https://api.rayuela.example.com/api/v1/accounts/me \
  -H "X-API-Key: tu_api_key_aquí"
```

Deberías recibir información básica sobre tu cuenta, lo que confirma que tu API Key es válida.

## 6. Carga de Datos Básicos

Para obtener recomendaciones, primero necesitas cargar algunos datos. Aquí hay ejemplos para cargar productos, usuarios e interacciones.

### Cargar Productos

```bash
curl -X POST https://api.rayuela.example.com/api/v1/products \
  -H "X-API-Key: tu_api_key_aquí" \
  -H "Content-Type: application/json" \
  -d '{
    "external_id": "prod-001",
    "name": "Smartphone XYZ",
    "description": "Smartphone de última generación con cámara de 48MP",
    "price": 599.99,
    "category": "electronics",
    "attributes": {
      "color": "black",
      "brand": "XYZ",
      "memory": "128GB"
    }
  }'
```

### Cargar Usuarios

```bash
curl -X POST https://api.rayuela.example.com/api/v1/users \
  -H "X-API-Key: tu_api_key_aquí" \
  -H "Content-Type: application/json" \
  -d '{
    "external_id": "user-001",
    "attributes": {
      "age": 28,
      "gender": "female",
      "location": "Madrid"
    }
  }'
```

### Registrar Interacciones

```bash
curl -X POST https://api.rayuela.example.com/api/v1/interactions \
  -H "X-API-Key: tu_api_key_aquí" \
  -H "Content-Type: application/json" \
  -d '{
    "user_external_id": "user-001",
    "product_external_id": "prod-001",
    "interaction_type": "view",
    "timestamp": "2023-06-15T14:30:00Z",
    "value": 1.0
  }'
```

### Carga Masiva (Opcional)

Para cargar muchos datos a la vez, usa el endpoint de carga masiva:

```bash
curl -X POST https://api.rayuela.example.com/api/v1/batch \
  -H "X-API-Key: tu_api_key_aquí" \
  -H "Content-Type: application/json" \
  -d '{
    "users": [
      {
        "external_id": "user-002",
        "attributes": { "age": 35, "gender": "male" }
      },
      {
        "external_id": "user-003",
        "attributes": { "age": 42, "gender": "female" }
      }
    ],
    "products": [
      {
        "external_id": "prod-002",
        "name": "Auriculares Bluetooth",
        "category": "accessories",
        "price": 89.99
      },
      {
        "external_id": "prod-003",
        "name": "Tablet Ultra",
        "category": "electronics",
        "price": 349.99
      }
    ],
    "interactions": [
      {
        "user_external_id": "user-002",
        "product_external_id": "prod-002",
        "interaction_type": "purchase",
        "timestamp": "2023-06-15T15:45:00Z",
        "value": 1.0
      },
      {
        "user_external_id": "user-003",
        "product_external_id": "prod-003",
        "interaction_type": "view",
        "timestamp": "2023-06-15T16:20:00Z",
        "value": 1.0
      }
    ]
  }'
```

## 7. Entrenamiento del Modelo

Después de cargar suficientes datos, entrena tu modelo de recomendación:

```bash
curl -X POST https://api.rayuela.example.com/api/v1/pipeline/train \
  -H "X-API-Key: tu_api_key_aquí"
```

Este proceso puede tardar varios minutos dependiendo de la cantidad de datos. Puedes verificar el estado del entrenamiento:

```bash
curl -X GET https://api.rayuela.example.com/api/v1/pipeline/jobs/1/status \
  -H "X-API-Key: tu_api_key_aquí"
```

## 8. Obtención de Recomendaciones

Una vez que el modelo esté entrenado, puedes obtener recomendaciones:

### Recomendaciones Personalizadas para un Usuario

```bash
curl -X GET "https://api.rayuela.example.com/api/v1/recommendations/user-001?limit=5" \
  -H "X-API-Key: tu_api_key_aquí"
```

Respuesta:

```json
{
  "items": [
    {
      "item_id": 3,
      "name": "Tablet Ultra",
      "description": "Tablet de alta resolución con procesador rápido",
      "price": 349.99,
      "category": "electronics",
      "score": 0.92,
      "source": "hybrid"
    },
    {
      "item_id": 2,
      "name": "Auriculares Bluetooth",
      "description": "Auriculares inalámbricos con cancelación de ruido",
      "price": 89.99,
      "category": "accessories",
      "score": 0.85,
      "source": "collaborative"
    },
    // ... más recomendaciones
  ],
  "total": 5,
  "page": 1,
  "size": 5
}
```

### Recomendaciones por Categoría

```bash
curl -X GET "https://api.rayuela.example.com/api/v1/recommendations/user-001?category=electronics&limit=5" \
  -H "X-API-Key: tu_api_key_aquí"
```

### Productos Populares (Para Usuarios Nuevos)

```bash
curl -X GET "https://api.rayuela.example.com/api/v1/recommendations/popular-trends?limit=5" \
  -H "X-API-Key: tu_api_key_aquí"
```

## 9. Siguientes Pasos

Una vez que hayas completado estos pasos básicos, puedes:

1. Explorar la documentación completa en `/api/docs` o `/api/redoc`
2. Implementar la integración en tu aplicación
3. Configurar webhooks para recibir notificaciones de eventos
4. Explorar características avanzadas como A/B testing y segmentación

## Recursos Adicionales

- [Documentación Completa de la API](https://api.rayuela.example.com/api/docs)
- [Guía de Integración](https://docs.rayuela.example.com/integration)
- [Ejemplos de Código](https://github.com/rayuela-examples)
- [Preguntas Frecuentes](https://docs.rayuela.example.com/faq)

¿Necesitas ayuda? Contáctanos en [<EMAIL>](mailto:<EMAIL>)
